{"groups": [{"keys": "loading_json", "name": "loading"}, {"keys": "32_fnt,28_blue_fnt,road_middle_png,home,home2,mapIcon_json,props_new_json,startProps_json,props_shadow_png,homeBottom_json,modal_common_json,modal_common_grid_json", "name": "homeScene"}, {"keys": "road_begin_png", "name": "roadMapBegin"}, {"keys": "road_end_png", "name": "roadMapEnd"}, {"keys": "pcpage_png", "name": "pcScene"}, {"keys": "home_bg_mp3,click_mp3", "name": "homeSceneSound"}, {"keys": "help_image_jpeg,help_modal_bg_png,help_title_png", "name": "helpGroup"}, {"keys": "bundle_json", "name": "bundleGroup"}, {"keys": "task_json,common_progress_json", "name": "taskGroup"}, {"keys": "common_progress_json", "name": "commonProgressGroup"}, {"keys": "unlock_json", "name": "unlockGroup"}, {"keys": "stagerush_json", "name": "stagerushEventGroup"}, {"keys": "tabViewCommon_json", "name": "tabViewCommonGroup"}, {"keys": "ingameProps_json", "name": "inGamePropsGroup"}, {"keys": "otherCell", "name": "cellStaticGroup"}, {"keys": "big_light,reward_common_json,task_completed", "name": "stagerushGroup"}, {"keys": "stagerush_sc_ske_json,stagerush_sc_tex_json,stagerush_sc_tex_png", "name": "stagerushAnimationGroup"}, {"keys": "reward_common_json,ingame<PERSON><PERSON>_json", "name": "stageRushIconGroup"}, {"keys": "big_light", "name": "rewardViewGroup"}, {"keys": "reward_common_json,task_completed", "name": "publicComponentGroup"}, {"keys": "<PERSON><PERSON><PERSON>_ske_json,<PERSON><PERSON><PERSON>_tex_json,<PERSON><PERSON><PERSON>_tex_png", "name": "loginBonusAnimationGroup"}, {"keys": "<PERSON><PERSON><PERSON>_ske_json,<PERSON><PERSON><PERSON>_tex_json,<PERSON><PERSON><PERSON>_tex_png,loginBonusNew<PERSON>ser_json", "name": "loginBonusNewUserAnimationGroup"}, {"keys": "loginBonusNewUser_json", "name": "loginBonusNewUserGroup"}, {"keys": "loginBonus_json", "name": "loginBonusGroup"}, {"keys": "middleNormal_json,game_words,guide_gestures_png,modal_bg_png,light_bg_json,props_new_json,32_fnt,quarter_modal_light_png,middleCell,otherCell,modal_common_json,modal_common_grid_json,game_modal_json,big_light", "name": "commonGameGroup"}, {"keys": "middle<PERSON><PERSON><PERSON>_<PERSON><PERSON>,middle<PERSON>ell,otherCell", "name": "commonGameElementGroup"}, {"keys": "digRank_json", "name": "digRankGroup"}, {"keys": "congratulation_json", "name": "congratulationResGroup"}, {"keys": "rankIcon_json", "name": "rankIconGroup"}, {"keys": "default_avatar", "name": "defaultAvatarGroup"}, {"keys": "default_rect_avatar", "name": "defaultRectAvatarGroup"}, {"keys": "home,mapIcon_json", "name": "homeIconGroup"}, {"keys": "ingameProps_json,startProps_json", "name": "propsGroup"}, {"keys": "props_new_json", "name": "propsRewardGroup"}, {"keys": "coins_more_png", "name": "extraPropsGroup"}, {"keys": "home,props_shadow_png,mapIcon_json", "name": "homeCommonGroup"}, {"keys": "light_ts_ske_json,light_ts_tex_json,light_ts_tex_png", "name": "digIconAnimationGroup"}, {"keys": "winStreak,winStreakBox_tex_json,winStreakBox_ske_json,winStreakBox_tex_png", "name": "winStreakAnimationGroup"}, {"keys": "commonPanel_json,celebrate_title_tex_png,celebrate_title_ske_json,celebrate_title_tex_json,props_shadow_png,mainCommonPanel_json", "name": "commonPanelAnimationGroup"}, {"keys": "guide_gestures_png,common_guide_json", "name": "commonGuideGroup"}, {"keys": "store_bg_png,popup_json,popup_bg_png,egg_json,store_json", "name": "redeem"}, {"keys": "native_upgrade", "name": "nativeUpgradeGroup"}, {"keys": "exit_modal_json", "name": "exitModalGroup"}, {"keys": "z<PERSON>yue_box_ske_json,z<PERSON>yu<PERSON>_box_tex_json,zhaiyue_box_tex_png,collectibleToken_json", "name": "tokenBundleAnimationGroup"}, {"keys": "audio_collecting_success_mp3,audio_double_rewards_mp3,audio_gift_collecting_1_mp3,audio_gift_collecting_2_mp3,audio_gift_collecting_3_mp3,audio_gift_collecting_4_mp3,audio_gift_pack_mp3", "name": "tokenBundleSoundGroup"}, {"keys": "free_icom_ske_json,free_icom_tex_json,free_icom_tex_png", "name": "freeShareBgAnimationGroup"}, {"keys": "collectible_common_json", "name": "collectibleCommonGroup"}, {"keys": "PurchaseComplete_sc_tex_png,PurchaseComplete_sc_ske_json,PurchaseComplete_sc_tex_json", "name": "chanceModalAnimationGroup"}, {"keys": "chance_modal_json,commonPanel_json,light_bg_json,audio_purchase_complete_mp3,audio_booster_added_mp3,chance_modal_common_json,modal_bg_png", "name": "chanceModalGroup"}, {"keys": "cell.config_json", "name": "cellConfigGroup"}, {"keys": "share_point_modal_json,text_move_font_png_0,text_move_font_png_1,share_move_png_0,share_move_png_1,sharing_ins_png,sharing_other_png,audio_sharing_popup_mp3,platform_move_png_0,platform_move_png_1", "name": "sharePointGroup"}, {"keys": "welcome_back", "name": "fontTitle"}, {"keys": "get_time_limited_reward_mp3", "name": "timeLimitedRewardSoundGroup"}, {"keys": "inbox_json,reward_common_json", "name": "inboxGroup"}, {"keys": "share_bundle_icon_json,giftbag_ske_json,giftbag_tex_json,giftbag_tex_png", "name": "shareBundleIconAnimationGroup"}, {"keys": "share_bundle_modal_json", "name": "shareBundleModalGroup"}, {"keys": "common_rank_settle_json", "name": "commonRankSettleGroup"}, {"keys": "caidai_ske_json,caidai_tex_json,caidai_tex_png", "name": "streamerAnimationGroup"}, {"keys": "modal_common_json", "name": "modalCommonGroup"}, {"keys": "ldc_popup_json", "name": "ldcPopupGroup"}, {"keys": "machine_icon_ske_json,machine_icon_tex_png,machine_icon_tex_json", "name": "boxMachineIconAnimationGroup"}, {"keys": "adsChanceItem_json", "name": "adsChanceGroup"}, {"keys": "adsPropItem_json", "name": "adsPropGroup"}, {"keys": "celestialLbR<PERSON><PERSON>_json,celestial_lbRewardsStatic_json", "name": "celestialLbRewards"}, {"keys": "adsLandingPage", "name": "adsLandingPageGroup"}, {"keys": "adsForExtraMove_json", "name": "adsForExtraMoveGroup"}, {"keys": "GroupRaceForGame_json", "name": "groupRaceMainGroup"}, {"keys": "adsTip_json", "name": "adsTipGroup"}, {"keys": "adsChanceEntrance_json", "name": "adsChanceEntranceGroup"}, {"keys": "battlePassConfirmModal_json", "name": "battlePassConfirmModalGroup"}, {"keys": "endless_bundle_sc_ske_json,endless_bundle_sc_tex_json,endless_bundle_sc_tex_png", "name": "endlessBundleAnimationGroup"}, {"keys": "endlessBundleCommon_json,endlessBundle6Card_json", "name": "endlessBundle6CardGroup"}, {"keys": "endlessBundleCommon_json,endlessBundle3Card_json", "name": "endlessBundle3CardGroup"}, {"keys": "makeover_settle_json", "name": "makeoverSettleGroup"}, {"keys": "exchangeRewardsStatic_json", "name": "exchangeRewardsStaticGroup"}, {"keys": "envelopePet_json,envelopeCommon_json", "name": "loginEnvelopeBasicGroup"}, {"keys": "loginEnvelope_json", "name": "loginEnvelopeLowGroup"}, {"keys": "mail_Animation_ske_json,mail_Animation_tex_json,mail_Animation_tex_png", "name": "loginEnvelopeAnimationGroup"}, {"keys": "audio_envelop_flying_in_mp3,audio_envelop_open_mp3", "name": "loginEnvelopeSoundGroup"}, {"keys": "tips_guide_bg,redeem_guide_reward_png", "name": "redeemGuideGroup"}, {"keys": "rankNumber_fnt", "name": "rankFntGroup"}, {"keys": "cloth_box_tex_png,cloth_box_ske_json,cloth_box_tex_json", "name": "stageRewardAnimationGroup"}, {"keys": "commonLeaderBoardLine,commonLeaderBoardLine2", "name": "CommonLeaderBoardGroup"}, {"keys": "modal_bg_png", "name": "BaseModalBgGroup"}, {"keys": "mainCommonPanel_json", "name": "mainCommonPanelGroup"}, {"keys": "commonPanel_json", "name": "commonPanelJSONGroup"}, {"keys": "celestial_lbRewards<PERSON><PERSON><PERSON>_json,cloudlandSettle_json", "name": "cloudlandSettleGroup"}, {"keys": "stagerush_box_json", "name": "stagerushBoxGroup"}, {"keys": "HiddenCandy_icon_tex_png,HiddenCandy_icon_tex_json,HiddenCandy_icon_ske_json", "name": "pickaxeIconAnimationGroup"}, {"keys": "reward_common_json", "name": "rewardCommonGroup"}, {"keys": "click_mp3", "name": "commonSoundGroup"}, {"keys": "bank_json,commonPanel_json", "name": "bankGroup"}, {"keys": "audio_bank_gift_received_mp3", "name": "bankSoundGroup"}, {"keys": "sureTo<PERSON><PERSON>_json", "name": "sureToLoseGroup"}], "resources": [{"name": "assets.map_json", "type": "json", "url": "main/config/assets.map.json"}, {"name": "home_bg_mp3", "type": "sound", "url": "main/assets/sounds/home_bg.mp3"}, {"name": "game_words", "subkeys": "bonus,combo_2,combo_3,combo_4,combo_5,combo_6,complete,move_relayout,no_more", "type": "sheet", "url": "main/assets/map/gamewords.json"}, {"name": "32_fnt", "type": "font", "url": "main/assets/fonts/32-astc.fnt"}, {"name": "28_blue_fnt", "type": "font", "url": "main/assets/fonts/28_blue-astc.fnt"}, {"name": "road_begin_png", "type": "image", "url": "main/assets/home/<USER>"}, {"name": "road_middle_png", "type": "image", "url": "main/assets/home/<USER>"}, {"name": "road_end_png", "type": "image", "url": "main/assets/home/<USER>"}, {"name": "home", "subkeys": "button_chain,ftw_btn_start,ftw_tips,help,inbox_icon,infinite_chance_bg,infinite_chance_progress,main_btn_start,music_close,music_close_grey,music_open,music_open_grey,new,quick_start,red_dot,share,image_level_tutorial_entrance", "type": "sheet", "url": "main/assets/home/<USER>"}, {"name": "adsLandingPage", "subkeys": "mainpage_icon_ads,mainpage_icon_ads_store,mainpage_icon_ads_tip", "type": "sheet", "url": "main/assets/adsLandingPage/adsLandingPage.json"}, {"name": "home2", "subkeys": "countdown,market_close,player_avatar,player_avatar_arrow,player_avatar_me,player_avatar_me_arrow,roundthumb,triangle", "type": "sheet", "url": "main/assets/home/<USER>"}, {"name": "map_bg_png", "type": "image", "url": "main/assets/map/map_bg.png"}, {"name": "modal_bg_png", "scale9grid": "80,82,83,87", "type": "image", "url": "main/assets/modal/modal_bg.png"}, {"name": "quarter_modal_light_png", "type": "image", "url": "main/assets/modal/quarter_modal_light.png"}, {"name": "loading_json", "subkeys": "black_mask,loding_candy,loding_light,org_bg", "type": "sheet", "url": "native/assets/loading/loading-astc.json"}, {"name": "guide_gestures_png", "type": "image", "url": "main/assets/guide/guide_gestures.png"}, {"name": "pcpage_png", "type": "image", "url": "main/assets/pc/pcpage.png"}, {"name": "task_json", "subkeys": "task_nothing,time_clock,task_light,title_bg,icon_gift_03,icon_gift_02,icon_gift_01,task_icon_share", "type": "sheet", "url": "main/assets/task/task.json"}, {"name": "super_rainbow_ske_json", "type": "json", "url": "main/assets/armature/rainbow/super_rainbow_ske.json"}, {"name": "super_rainbow_tex_json", "type": "json", "url": "main/assets/armature/rainbow/super_rainbow_tex.json"}, {"name": "super_rainbow_tex_png", "type": "astc", "url": "main/assets/armature/rainbow/super_rainbow_tex.astc"}, {"name": "common_progress_json", "subkeys": "99_progress_01,99_progress_02,stagerush_progress_01,stagerush_progress_02,task_bar,task_bar_bg", "type": "sheet", "url": "common/assets/progress/common_progress.json"}, {"name": "click_mp3", "type": "sound", "url": "main/assets/sounds/click.mp3"}, {"name": "help_image_jpeg", "type": "image", "url": "main/assets/modal/help_image.jpeg"}, {"name": "props_new_json", "subkeys": "backpack_bg,panel_blue,props_display_button,props_display_selected,reward_bg,backpack_discount_label,image_inventory_bg,image_inventory_countdown,little_green_panel,props_green_check_button,props_unlock_tips,image_inventory_icon,infinite", "type": "sheet", "url": "main/assets/props/props_new.json"}, {"name": "coins_more_png", "type": "astc", "url": "main/assets/props/coins_more.astc"}, {"name": "props_shadow_png", "type": "astc", "url": "main/assets/props/props_shadow.astc"}, {"name": "bundle_json", "subkeys": "bottom_frame,bottom_frame_big,bottom_frame_big_light,bottom_frame_light,bottom_plate,day_1,day_2,day_3,day_4,day_5,day_6,day_7,discount80_icon,get,miss,new_user_gifts_bg,new_user_title_png,seven_day_bundle_banner,seven_day_bundle_title,time_frame,time_icon,today,tomorrow", "type": "sheet", "url": "main/assets/gifts/bundle.json"}, {"name": "seven_day_bundle_icon", "type": "image", "url": "main/assets/gifts/seven_day_bundle_icon.png"}, {"name": "autoBundle1_json", "subkeys": "bg_auto_bundle_1,bg_time_auto_bundle_1,icon_time_auto_bundle_1,panel_auto_bundle_1,title_auto_bundle_1", "type": "sheet", "url": "main/assets/gifts/autoBundle1.json"}, {"name": "autoBundle2_json", "subkeys": "bg_auto_bundle_2,bg_time_auto_bundle_2,icon_time_auto_bundle_2,panel_auto_bundle_2,title_auto_bundle_2", "type": "sheet", "url": "main/assets/gifts/autoBundle2.json"}, {"name": "autoBundle3_json", "subkeys": "bg_auto_bundle_3,bg_time_auto_bundle_3,icon_time_auto_bundle_3,panel_auto_bundle_3,title_auto_bundle_3", "type": "sheet", "url": "main/assets/gifts/autoBundle3.json"}, {"name": "autoBundle4_json", "subkeys": "bg_auto_bundle_4,bg_time_auto_bundle_4,icon_time_auto_bundle_4,panel_auto_bundle_4,title_auto_bundle_4", "type": "sheet", "url": "main/assets/gifts/autoBundle4.json"}, {"name": "diamond_png", "type": "image", "url": "main/assets/props/diamond.png"}, {"name": "unlock_json", "subkeys": "banner,bottom_plate,lock,one_star_level,two_stars_level,unlock_method,zero_star_level", "type": "sheet", "url": "main/assets/unlock/unlock.json"}, {"name": "loading_banner_png", "type": "astc", "url": "loading/loading_banner.astc"}, {"name": "tabViewCommon_json", "subkeys": "wood_board_bottom,wood_board_top", "type": "sheet", "url": "main/assets/tabView/tabViewCommon.json"}, {"name": "exit_modal_json", "subkeys": "exit_guide_phone,exit_lucky_draw,exit_panel,exit_title", "type": "sheet", "url": "main/assets/exitModal/exit_modal.json"}, {"name": "modal_common_json", "subkeys": "list_live_background,list_background,time_frame,common_button,title_modal,modal_button_gray,button_primary,button_default,earn_more_coins,lives_bundle_bcg,common_panel_2,startPropsTip_half,button_dark,button_light,star_empty_modal,btn_play_1,btn_play_3,image_start_game_popup_reward_box,pass_token,coins,img_tag_double,diamond,star_fill_modal,life,common_panel_title_1,common_panel_title_2,img_font_double,lives_bundle_decoration,balance_add_png,image_icon_small,close,life_broken,free_life,free_life_grey,get,common_panel_line", "type": "sheet", "url": "main/assets/modal/modal_common.json"}, {"name": "modal_common_grid_json", "subkeys": "common_panel_1,panel_orange_title", "type": "sheet", "url": "main/assets/modal/modal_common_grid.json"}, {"name": "middleNormal_json", "subkeys": "N1,N2,N3,N4,N5", "type": "sheet", "url": "main/assets/map/middleNormal.json"}, {"name": "stagerush_json", "subkeys": "bottom_stage_rush_1,claimed", "type": "sheet", "url": "main/assets/task/stagerush.json"}, {"name": "reward_common_json", "subkeys": "bg_2,bg_countdown,bg_gx,icon_reward,icon_time,popup_rewards,popup_rewards_arrow,progress_1,progress_2,ribbon,stage_reward_star", "type": "sheet", "url": "common/assets/reward/reward_common-astc.json"}, {"name": "loginBonus_json", "subkeys": "LDC,bottom_login_bonus,bottom_login_bonus_today,coins,login_bonus_title,package_1,package_2,package_3,package_4,package_5,package_6,package_7,progress_bar,progress_bar_claimed,reward_box,round_date,round_date_claimed,slide_arrow,tomorrow_sbscript,voucher", "type": "sheet", "url": "main/assets/loginBonus/loginBonus.json"}, {"name": "<PERSON><PERSON><PERSON>_ske_json", "type": "json", "url": "main/assets/armature/Landingreward/Landingreward_ske.json"}, {"name": "<PERSON><PERSON><PERSON>_tex_json", "type": "json", "url": "main/assets/armature/Landingreward/Landingreward_tex.json"}, {"name": "Landingreward_tex_png", "type": "astc", "url": "main/assets/armature/Landingreward/Landingreward_tex.astc"}, {"name": "loginBonusNewUser_json", "subkeys": "LDC,bottom_login_bonus,bottom_login_bonus_today,coins,login_bonus_title,package_1,package_2,package_3,package_4,package_5,package_6,package_7,progress_bar,progress_bar_claimed,reward_box,round_date,round_date_claimed,slide_arrow,tomorrow_sbscript,voucher", "type": "sheet", "url": "main/assets/loginBonus/loginBonusNewUser.json"}, {"name": "loginBonusIcon_png", "type": "image", "url": "main/assets/loginBonus/loginBonusIcon.png"}, {"name": "loginBonusNewUserIcon_png", "type": "image", "url": "main/assets/loginBonus/loginBonusNewUserIcon.png"}, {"name": "loginBonusClaimTip_png", "type": "image", "url": "main/assets/loginBonus/loginBonusClaimTip.png"}, {"name": "digRank_json", "subkeys": "congratulation,reware_title,settlement_IP,settlement_bottom", "type": "sheet", "url": "main/assets/digRank/digRank.json"}, {"name": "congratulation_json", "subkeys": "congratulation,reware_title", "type": "sheet", "url": "common/assets/congratulation/congratulation.json"}, {"name": "help_modal_bg_png", "type": "image", "url": "common/assets/modal/help_modal_bg.png"}, {"name": "help_title_png", "type": "image", "url": "common/assets/modal/help_title.png"}, {"name": "rankIcon_json", "subkeys": "bronze_crown,golden_crown,silver_crown", "type": "sheet", "url": "common/assets/rank/rankIcon-astc.json"}, {"name": "commonPanel_json", "subkeys": "btn_orange_s,discount2,font_PurchaseComplete,font_congratulation,store_frame_01,store_frame_02,stroke_btn,title_yellow,btn_common_big_orange,btn_common_big_gray", "type": "sheet", "url": "common/assets/panel/commonPanel.json"}, {"name": "default_avatar", "type": "astc", "url": "common/assets/avatar/default_avatar.astc"}, {"name": "default_rect_avatar", "type": "astc", "url": "common/assets/avatar/default_rect_avatar.astc"}, {"name": "task_completed", "type": "image", "url": "common/assets/reward/task_completed.png"}, {"name": "winStreak", "subkeys": "ID_ip_design,bg,bg_bubble,bottom,bottom_light,bottom_reward,bottom_win_streak,lose_win_streak,losing,title,ui_bottom", "type": "sheet", "url": "main/assets/winStreak/winStreak.json"}, {"name": "winStreakBox_tex_json", "type": "json", "url": "main/assets/armature/winStreakBox/winStreakBox_tex.json"}, {"name": "winStreakBox_ske_json", "type": "json", "url": "main/assets/armature/winStreakBox/winStreakBox_ske.json"}, {"name": "winStreakBox_tex_png", "type": "astc", "url": "main/assets/armature/winStreakBox/winStreakBox_tex.astc"}, {"name": "entrance_auto_bundle_1", "type": "image", "url": "main/assets/gifts/entrance_auto_bundle_1.png"}, {"name": "entrance_auto_bundle_2", "type": "image", "url": "main/assets/gifts/entrance_auto_bundle_2.png"}, {"name": "entrance_auto_bundle_3", "type": "image", "url": "main/assets/gifts/entrance_auto_bundle_3.png"}, {"name": "entrance_auto_bundle_4", "type": "image", "url": "main/assets/gifts/entrance_auto_bundle_4.png"}, {"name": "celebrate_title_tex_png", "type": "astc", "url": "common/assets/armature/celebrateTitle/celebrate_title_tex.astc"}, {"name": "celebrate_title_ske_json", "type": "json", "url": "common/assets/armature/celebrateTitle/celebrate_title_ske.json"}, {"name": "celebrate_title_tex_json", "type": "json", "url": "common/assets/armature/celebrateTitle/celebrate_title_tex.json"}, {"name": "PurchaseComplete_sc_tex_png", "type": "astc", "url": "common/assets/armature/chanceRewardTitle/PurchaseComplete_sc_ske_tex.astc"}, {"name": "PurchaseComplete_sc_ske_json", "type": "json", "url": "common/assets/armature/chanceRewardTitle/PurchaseComplete_sc_ske_ske.json"}, {"name": "PurchaseComplete_sc_tex_json", "type": "json", "url": "common/assets/armature/chanceRewardTitle/PurchaseComplete_sc_ske_tex.json"}, {"name": "modal_error", "subkeys": "error_btn,load_error", "type": "sheet", "url": "native/assets/modal_error/modal_error-astc.json"}, {"name": "store_bg_png", "type": "image", "url": "main/assets/redeem/bg.png"}, {"name": "popup_json", "subkeys": "popup_coupon,use_button", "type": "sheet", "url": "main/assets/redeem/popup-astc.json"}, {"name": "popup_bg_png", "type": "image", "url": "main/assets/redeem/popup_bg.png"}, {"name": "egg_json", "subkeys": "egg01,egg02,use_button_gray", "type": "sheet", "url": "main/assets/redeem/egg-astc.json"}, {"name": "store_json", "subkeys": "item_full,lucky_full,store_diamond,top", "type": "sheet", "url": "main/assets/redeem/store.json"}, {"name": "prizes_bg_png", "type": "astc", "url": "main/assets/redeem/prizes_bg.astc"}, {"name": "native_upgrade", "subkeys": "upgrade_bg,upgrade_btn,upgrade_mascot", "type": "sheet", "url": "main/assets/nativeUpgrade/native_upgrade.json"}, {"name": "collectibleToken_json", "subkeys": "btn_light,font_newredeemable,icon_popup,item_panel,panel_5", "type": "sheet", "url": "main/assets/collectibleToken/collectibleToken.json"}, {"name": "common_popup_rank_png", "type": "image", "url": "common/assets/reward/common_popup_rank.png"}, {"name": "cell.config_json", "type": "json", "url": "main/config/cell.config.json"}, {"name": "<PERSON><PERSON><PERSON><PERSON>_box_ske_json", "type": "json", "url": "main/assets/armature/tokenBundle/zhaiyue_box_ske.json"}, {"name": "<PERSON><PERSON><PERSON><PERSON>_box_tex_json", "type": "json", "url": "main/assets/armature/tokenBundle/zhaiyue_box_tex.json"}, {"name": "z<PERSON><PERSON><PERSON>_box_tex_png", "type": "astc", "url": "main/assets/armature/tokenBundle/zhaiyue_box_tex.astc"}, {"name": "audio_collecting_success_mp3", "type": "sound", "url": "main/assets/sounds/tokenBundle/audio_collecting_success.mp3"}, {"name": "audio_double_rewards_mp3", "type": "sound", "url": "main/assets/sounds/tokenBundle/audio_double_rewards.mp3"}, {"name": "audio_gift_collecting_1_mp3", "type": "sound", "url": "main/assets/sounds/tokenBundle/audio_gift_collecting_1.mp3"}, {"name": "audio_gift_collecting_2_mp3", "type": "sound", "url": "main/assets/sounds/tokenBundle/audio_gift_collecting_2.mp3"}, {"name": "audio_gift_collecting_3_mp3", "type": "sound", "url": "main/assets/sounds/tokenBundle/audio_gift_collecting_3.mp3"}, {"name": "audio_gift_collecting_4_mp3", "type": "sound", "url": "main/assets/sounds/tokenBundle/audio_gift_collecting_4.mp3"}, {"name": "audio_gift_pack_mp3", "type": "sound", "url": "main/assets/sounds/tokenBundle/audio_gift_pack.mp3"}, {"name": "free_icom_ske_json", "type": "json", "url": "common/assets/armature/free_icom/free_icom_ske.json"}, {"name": "free_icom_tex_json", "type": "json", "url": "common/assets/armature/free_icom/free_icom_tex.json"}, {"name": "free_icom_tex_png", "type": "astc", "url": "common/assets/armature/free_icom/free_icom_tex.astc"}, {"name": "collectible_common_json", "subkeys": "btn_close,bundle_time_icon,ramadan_Congratulations,ramadan_add,tittle_green", "type": "sheet", "url": "common/assets/collectibleCommon/collectibleCommon.json"}, {"name": "audio_sharing_popup_mp3", "type": "sound", "url": "main/assets/sounds/audio_sharing_popup.mp3"}, {"name": "audio_purchase_complete_mp3", "type": "sound", "url": "main/assets/sounds/chanceBundle/audio_purchase_complete.mp3"}, {"name": "audio_booster_added_mp3", "type": "sound", "url": "main/assets/sounds/chanceBundle/audio_booster_added.mp3"}, {"name": "bau_ramadan_bundle_icon_png", "type": "image", "url": "main/assets/ramadanBundle/bauRamadanBundleIcon.png"}, {"name": "big_light", "type": "astc", "url": "common/assets/panel/big_light.astc"}, {"name": "middle<PERSON>ell", "subkeys": "MPZ_<PERSON>LA<PERSON>,MKE0,<PERSON>P1,MBG2_DOWN,MBG2,MB1,MBG1,MG1,MMB0,MMB,MMB0_EARS,MMB0_HOLE1,MMB0_HOLE2,N1R1,N2R1,N3R1,N4R1,N5R1,MCK1,<PERSON>K2,MCK3,MGE1_<PERSON>,MST3,MST4,MBO2,MBO3,MBO4,MBO5,MBO6,MBO1,MR0,MKE,MRB1,MRR1,MTO0,MTO1,MTO2,MTO3,MRB3,S5,MST2,MJJ2,MKT5,MRB2,MJJ1,S4,MJJ3,<PERSON>KT4,MKT3,S1,S2,S3,MST1,MGE1_BT,NO1,MPZ_G<PERSON><PERSON>,MPZ_RED,MBG4_DOWN,MC1,MBG4_UP,MB1_TARGET,MBG5_DOWN,MBG5_UP,MBG4_H,MBG5_<PERSON>,<PERSON><PERSON>1,<PERSON><PERSON>2,<PERSON><PERSON>3,<PERSON><PERSON>4,<PERSON><PERSON>5,<PERSON>E1_TP,<PERSON>G3_DOW<PERSON>,MBG3_<PERSON>,MBG3_<PERSON>,<PERSON><PERSON>T2,<PERSON><PERSON>_<PERSON>,MKT1,MIB1,MIB2,<PERSON>B", "type": "sheet", "url": "main/assets/map/middleCell-astc.json"}, {"name": "otherCell", "subkeys": "CB1,CB<PERSON>,CBU1,CBU2,CC1,CC2,CC3,CC4,CE1,CE1_C,CE1_D,CE1_L,CE1_W,CF1,CF2,CI1,CI2,CI3,CJ1,CJ2,CJ3,CM1_BEAM1,CM1_BEAM2,CM1_CLOTH1,CM1_CLOTH2,CM1_TARGET,CT1,CT2,LB1,LB1_LARGE,LF,LF_SMOKE,LJ1,LL1,LL2,LO1,LO1_HD,LO1_HM,LO1_HU,LO1_LD,LO1_LU,LO1_RD,LO1_RU,LO1_VL,LO1_VM,LO1_VR,LPI,LPI_L,LPO,LPO_L,LR1,LV,LVA,LVC,OTHER_LIGHT,OTHER_SELECT", "type": "sheet", "url": "main/assets/map/otherCell-astc.json"}, {"name": "light_ts_ske_json", "type": "json", "url": "main/assets/armature/light_ts/light_ts_ske.json"}, {"name": "light_ts_tex_json", "type": "json", "url": "main/assets/armature/light_ts/light_ts_tex.json"}, {"name": "light_ts_tex_png", "type": "astc", "url": "main/assets/armature/light_ts/light_ts_tex.astc"}, {"name": "chance_modal_json", "subkeys": "chance,change_btn,gray_btn,life_dot_1,life_dot_2,orange_btn,panel_light_1,panel_light_2,purchase_complete_life,yellow_btn", "type": "sheet", "url": "main/assets/chanceModal/chance_modal.json"}, {"name": "chance_modal_common_json", "subkeys": "popup_broken_line,popup_cutoff_line", "type": "sheet", "url": "main/assets/chanceModal/chance_modal_common-astc.json"}, {"name": "share_point_modal_json", "subkeys": "img_tag_free,img_tape,panel_share,red_btn,txt_0,txt_1,txt_2,txt_3,txt_4,txt_5,txt_6,txt_7,txt_8,txt_9", "type": "sheet", "url": "common/assets/sharePointModal/share_point_modal.json"}, {"name": "text_move_font_png_0", "type": "image", "url": "common/assets/sharePointModal/text_move_font.png"}, {"name": "share_move_png_0", "type": "image", "url": "common/assets/sharePointModal/share_move.png"}, {"name": "platform_move_png_0", "type": "image", "url": "common/assets/sharePointModal/platform_move.jpg"}, {"name": "text_move_font_png_1", "type": "image", "url": "common/assets/sharePointModal/text_move_font.png"}, {"name": "share_move_png_1", "type": "image", "url": "common/assets/sharePointModal/share_move.png"}, {"name": "platform_move_png_1", "type": "image", "url": "common/assets/sharePointModal/platform_move.jpg"}, {"name": "sharing_ins_png", "type": "image", "url": "common/assets/sharePointModal/sharing_ins.jpg"}, {"name": "sharing_other_png", "type": "image", "url": "common/assets/sharePointModal/sharing_other.jpg"}, {"name": "light_bg_json", "subkeys": "light_fog,small_light,small_shadow,yellow_panel_light", "type": "sheet", "url": "main/assets/lights/light_bg-astc.json"}, {"name": "welcome_back", "type": "astc", "url": "common/assets/reward/font_welcomeback.astc"}, {"name": "get_time_limited_reward_mp3", "type": "sound", "url": "main/assets/sounds/get_time_limited_reward.mp3"}, {"name": "inbox_json", "subkeys": "inbox_bg,inbox_bottom_top,inbox_claim_btn,inbox_claim_btn_grey,inbox_line,inbox_new,inbox_no_message,inbox_panel,inbox_title", "type": "sheet", "url": "main/assets/inbox/inbox.json"}, {"name": "boss_guide_json", "subkeys": "guide_panel,guide_triangle", "type": "sheet", "url": "common/assets/guide/boss_common_guide.json"}, {"name": "share_bundle_icon_json", "subkeys": "btn_sharebundle,icon_share_bundle,share_bundle_bottom", "type": "sheet", "url": "main/assets/shareBundle/shareBundleIcon.json"}, {"name": "share_bundle_modal_json", "subkeys": "img_balloon,img_platform,ip_shareGift,panel_shareGift,shareGift_dialog,store_frame_02,title_blue", "type": "sheet", "url": "main/assets/shareBundle/shareBundleModal.json"}, {"name": "share_bundle_pic_other", "type": "image", "url": "main/assets/shareBundle/share_bundle_pic_other.jpg"}, {"name": "share_bundle_pic_ins", "type": "image", "url": "main/assets/shareBundle/share_bundle_pic_ins.jpg"}, {"name": "giftbag_ske_json", "type": "json", "url": "main/assets/armature/share_bundle_icon/giftbag_ske.json"}, {"name": "giftbag_tex_json", "type": "json", "url": "main/assets/armature/share_bundle_icon/giftbag_tex.json"}, {"name": "giftbag_tex_png", "type": "astc", "url": "main/assets/armature/share_bundle_icon/giftbag_tex.astc"}, {"name": "common_rank_settle_json", "subkeys": "choky_congratulation,close,congratulation_panel,font_rank_0,font_rank_1,font_rank_2,font_rank_3,font_rank_4,font_rank_5,font_rank_6,font_rank_7,font_rank_8,font_rank_9,infinite_ticket_panel,reward_title,text_group_reward,title,title_text_congratulation,title_your_ranking", "type": "sheet", "url": "main/assets/frenzySettle/frenzy_settle.json"}, {"name": "caidai_ske_json", "type": "json", "url": "main/assets/armature/streamer/caidai_ske.json"}, {"name": "caidai_tex_png", "type": "astc", "url": "main/assets/armature/streamer/caidai_tex.astc"}, {"name": "caidai_tex_json", "type": "json", "url": "main/assets/armature/streamer/caidai_tex.json"}, {"name": "ldc_icon_png", "type": "image", "url": "common/assets/ldcPopupPanel/ldc_icon.png"}, {"name": "ldc_popup_json", "subkeys": "ldc_arrow,ldc_bg", "type": "sheet", "url": "common/assets/ldcPopupPanel/ldc_popup.json"}, {"name": "machine_icon_ske_json", "type": "json", "url": "main/assets/armature/box_machine_icon/machine_icon_ske.json"}, {"name": "machine_icon_tex_png", "type": "astc", "url": "main/assets/armature/box_machine_icon/machine_icon_tex.astc"}, {"name": "machine_icon_tex_json", "type": "json", "url": "main/assets/armature/box_machine_icon/machine_icon_tex.json"}, {"name": "adsChanceItem_json", "subkeys": "ad_chance_bg,blue_btn,icon_store", "type": "sheet", "url": "main/assets/adsEntrance/adsChanceItem.json"}, {"name": "adsPropItem_json", "subkeys": "ball_blue,icon_store_small,minigame_start_bg,text_free", "type": "sheet", "url": "main/assets/adsEntrance/adsPropItem.json"}, {"name": "celestialLbRewards_json", "subkeys": "climbing_line02,climbing_rank_panel05,climbing_rank_panel07_scale9Grid,climbing_rank_title01,climbing_your_rank,climbing_your_reward", "type": "sheet", "url": "main/assets/celestialSettle/celestialLbRewards.json"}, {"name": "celestial_lbRewardsStatic_json", "subkeys": "choky_congratulation,close,font_rank_0,font_rank_1,font_rank_2,font_rank_3,font_rank_4,font_rank_5,font_rank_6,font_rank_7,font_rank_8,font_rank_9", "type": "sheet", "url": "main/assets/celestialSettle/celestial_lbRewardsStatic-astc.json"}, {"name": "adsForExtraMove_json", "subkeys": "btn_blue,btn_orange,common_add_2,icon_store,panel_light_2,prop_panel,rewards_tips,step_0,step_1,step_2,step_3,step_4,step_5", "type": "sheet", "url": "main/assets/adsEntrance/adsForExtraMove.json"}, {"name": "Baloo2-ExtraBold", "type": "ttf", "url": "common/assets/fonts/Baloo2-ExtraBold.ttf"}, {"name": "game_modal_json", "subkeys": "close_red,failure_popup_title,moveSteps,target_modal,tick,well_done", "type": "sheet", "url": "main/assets/modal/game_modal.json"}, {"name": "GroupRaceForGame_json", "subkeys": "bottom_stage_rush_2,btn_common_big_orange,event_1,event_panel,event_player_1,event_player_2,event_progress,event_rank_1,event_rank_2,event_rank_3,event_rank_goal,quit_logo,quit_pic", "type": "sheet", "url": "main/assets/GroupRaceForGame/GroupRaceForGame.json"}, {"name": "adsTip_json", "subkeys": "img_ads_tips,mainpage_icon_ads_2", "type": "sheet", "url": "common/assets/adsTip/adsTip.json"}, {"name": "adsChanceEntrance_json", "subkeys": "btn_common_mini_blue,icon_store", "type": "sheet", "url": "common/assets/adsChanceEntrance/adsChanceEntrance.json"}, {"name": "mainCommonPanel_json", "subkeys": "btn_common_big_blue,btn_common_big_orange,chance_panel_common", "type": "sheet", "url": "main/assets/mainCommonPanel/mainCommonPanel.json"}, {"name": "battlePassConfirmModal_json", "subkeys": "image_battle_pass_confirm_background,image_battle_pass_pay,image_battle_pass_picture,image_confirmation_title", "type": "sheet", "url": "common/assets/battlePassConfirmModal/battlePassConfirmModal.json"}, {"name": "ads_step_1_png", "type": "image", "url": "common/assets/adsMoves/step_1.png"}, {"name": "ads_step_2_png", "type": "image", "url": "common/assets/adsMoves/step_2.png"}, {"name": "ads_step_3_png", "type": "image", "url": "common/assets/adsMoves/step_3.png"}, {"name": "ads_step_4_png", "type": "image", "url": "common/assets/adsMoves/step_4.png"}, {"name": "ads_step_5_png", "type": "image", "url": "common/assets/adsMoves/step_5.png"}, {"name": "common_guide_json", "subkeys": "guide_panel,guide_triangle,mascot_guide", "type": "sheet", "url": "common/assets/guide/common_guide.json"}, {"name": "endless_bundle_sc_ske_json", "type": "json", "url": "main/assets/armature/endlessBundle/endless_bundle_sc_ske.json"}, {"name": "endless_bundle_sc_tex_json", "type": "json", "url": "main/assets/armature/endlessBundle/endless_bundle_sc_tex.json"}, {"name": "endless_bundle_sc_tex_png", "type": "astc", "url": "main/assets/armature/endlessBundle/endless_bundle_sc_tex.astc"}, {"name": "endlessBundleCommon_json", "subkeys": "arrow_panel01,arrow_panel02,arrow_panel03,arrow_small,bundle_time_icon,endless_time,green_select,img_lock_blue,reward_icon_44,tips", "type": "sheet", "url": "main/assets/endlessBundleCommon/endlessBundleCommon.json"}, {"name": "endlessBundle6Card_json", "subkeys": "panel_purple,panel_yellow,title", "type": "sheet", "url": "main/assets/endlessBundle6Card/endlessBundle6Card.json"}, {"name": "endlessBundle3Card_json", "subkeys": "panel_blue,panel_purple,panel_yellow,title", "type": "sheet", "url": "main/assets/endlessBundle3Card/endlessBundle3Card.json"}, {"name": "mapIcon_json", "subkeys": "ftw_level_icon,locked_hard_level,locked_level,main_boss_level,main_boss_level_2,more_level,point_current,point_lock,point_pass,s_point_lock,s_point_pass,star,star_filled,time_icon,unlock_bubblebg", "type": "sheet", "url": "main/assets/home/<USER>/mapIcon.json"}, {"name": "homeBottom_json", "subkeys": "image_new_panel_image,image_new_map_icon_2,image_team_icon,image_new_button_light_image_2,image_navigation_new_2,image_new_event_icon_2,image_new_shop_icon_2,image_new_redeem_store_icon_2,image_new_redeem_store_corner", "type": "sheet", "url": "main/assets/home/<USER>/homeBottom.json"}, {"name": "makeover_settle_json", "subkeys": "choky_congratulation,close,congratulation_panel,font_rank_0,font_rank_1,font_rank_2,font_rank_3,font_rank_4,font_rank_5,font_rank_6,font_rank_7,font_rank_8,font_rank_9,infinite_ticket_panel,reward_title,text_group_reward,title,title_text_congratulation,title_your_ranking", "type": "sheet", "url": "main/assets/makeover/makeover_settle-astc.json"}, {"name": "exchangeRewardsStatic_json", "subkeys": "bottom_reward,title_yellow", "type": "sheet", "url": "main/assets/exchangeReward/exchangeReward-astc.json"}, {"name": "makeoverFontTitle", "type": "image", "url": "main/assets/makeover/font_congratulation.png"}, {"name": "envelopePet_json", "subkeys": "image_card_chocky,image_envelope,image_envelope_chocky,image_welcome_title", "type": "sheet", "url": "main/assets/loginTimeLimitedReward/envelopePet/envelopePet.json"}, {"name": "loginEnvelope_json", "subkeys": "card_bag,card_flower_blue,card_flower_orange,card_flower_red,card_gift,card_grass,envelope,envelope_top,panel,panel_bottom", "type": "sheet", "url": "main/assets/loginTimeLimitedReward/loginEnvelope/loginEnvelope-astc.json"}, {"name": "mail_Animation_ske_json", "type": "json", "url": "main/assets/loginTimeLimitedReward/envelopeAnimation/mail_Animation_sc_ske.json"}, {"name": "mail_Animation_tex_json", "type": "json", "url": "main/assets/loginTimeLimitedReward/envelopeAnimation/mail_Animation_sc_tex.json"}, {"name": "mail_Animation_tex_png", "type": "astc", "url": "main/assets/loginTimeLimitedReward/envelopeAnimation/mail_Animation_sc_tex.astc"}, {"name": "audio_envelop_flying_in_mp3", "type": "sound", "url": "main/assets/sounds/loginEnvelope/audio_envelop_flying_in.mp3"}, {"name": "audio_envelop_open_mp3", "type": "sound", "url": "main/assets/sounds/loginEnvelope/audio_envelop_open.mp3"}, {"name": "envelopeCommon_json", "subkeys": "btn_claim,envelope_reward_bottom", "type": "sheet", "url": "main/assets/loginTimeLimitedReward/envelopeCommon/envelopeCommon-astc.json"}, {"name": "loading_picture", "type": "image", "url": "main/assets/loading/loading_picture.png"}, {"name": "startProps_json", "subkeys": "props_1_png,props_2_png,props_6_png", "type": "sheet", "url": "common/assets/startProps/startProps.json"}, {"name": "ingameProps_json", "subkeys": "ingameProps_501_png,ingameProps_502_png,ingameProps_503_png,ingameProps_504_png", "type": "sheet", "url": "common/assets/ingameProps/ingameProps.json"}, {"name": "commonLeaderBoardLine", "type": "astc", "url": "common/assets/leaderBoard/team_line.astc"}, {"name": "commonLeaderBoardLine2", "type": "astc", "url": "common/assets/leaderBoard/team_line_2.astc"}, {"name": "rankNumber_fnt", "type": "font", "url": "main/assets/fonts/rankNumber-astc.fnt"}, {"name": "tips_guide_bg", "type": "astc", "url": "main/assets/redeem/redeem_tips.astc"}, {"name": "redeem_guide_reward_png", "type": "image", "url": "main/assets/redeem/redeem_guide_reward.png"}, {"name": "cloth_box_tex_png", "type": "astc", "url": "common/assets/armature/reward/stageReward/cloth_box_tex.astc"}, {"name": "cloth_box_ske_json", "type": "json", "url": "common/assets/armature/reward/stageReward/cloth_box_ske.json"}, {"name": "cloth_box_tex_json", "type": "json", "url": "common/assets/armature/reward/stageReward/cloth_box_tex.json"}, {"name": "cloudlandSettle_json", "subkeys": "climbing_rank_title01,climbing_rank_panel07_scale9Grid,climbing_rank_panel05,climbing_your_rank,climbing_your_reward,climbing_line02", "type": "sheet", "url": "main/assets/cloudlandSettle/cloudlandSettle.json"}, {"name": "HiddenCandy_icon_tex_png", "type": "astc", "url": "main/assets/armature/pickaxeIcon/HiddenCandy_icon_tex.astc"}, {"name": "HiddenCandy_icon_tex_json", "type": "json", "url": "main/assets/armature/pickaxeIcon/HiddenCandy_icon_tex.json"}, {"name": "HiddenCandy_icon_ske_json", "type": "json", "url": "main/assets/armature/pickaxeIcon/HiddenCandy_icon_ske.json"}, {"name": "stagerush_sc_ske_json", "type": "json", "url": "main/assets/armature/stageRush/stagerush_sc_ske.json"}, {"name": "stagerush_sc_tex_json", "type": "json", "url": "main/assets/armature/stageRush/stagerush_sc_tex.json"}, {"name": "stagerush_sc_tex_png", "type": "astc", "url": "main/assets/armature/stageRush/stagerush_sc_tex.astc"}, {"name": "stagerush_box_json", "subkeys": "boxBody,boxLid", "type": "sheet", "url": "main/assets/stageRush/stagerush-astc.json"}, {"name": "bank_json", "subkeys": "bank_panel,bank_container,bank_IP,bank_title,bank_fill_3,image_bank_congratulation,bank_bottle_num,bank_fill_2,btn_common_big_yellow,bank_fill_1,icon,icon_store,bank_progress_1,bank_progress_2", "type": "sheet", "url": "main/assets/bank/bank-astc.json"}, {"name": "audio_bank_gift_received_mp3", "type": "sound", "url": "main/assets/bank/audio_bank_gift_received.mp3"}, {"name": "sureTo<PERSON><PERSON>_json", "subkeys": "image_fail_sec_bubble,image_fail_sec_title,image_fail_sec_mascot,image_fail_sec_frame,image_fail_sec_cross", "type": "sheet", "url": "main/assets/sureToLose/sureToLose.json"}, {"name": "SS4", "type": "astc", "url": "main/assets/map/superRainbow/SS4.astc"}, {"name": "image_fail_sec_winstreak_png", "type": "image", "url": "main/assets/sureToLose/loseRewards/image_fail_sec_winstreak.png"}, {"name": "help_btn_icon", "type": "image", "url": "main/assets/home/<USER>"}]}