// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`Tick Dynamic Difficulty Last Drop expect Generate missile then drop rainbow in 3 blank 1`] = `
[
  "MBL1",
  "S1",
  "S4",
  "N3",
  "N3",
  "N5",
  "N3",
  "N3",
  "N1",
  "N2",
]
`;

exports[`Tick Dynamic Difficulty Last Drop expect do not drop unavailable candy 1`] = `
[
  "MBL1",
  "N4",
  "N2",
  "N1",
  "N3",
  "MBL1",
  "MBL1",
  "N1",
  "MBL1",
  "MBL1",
]
`;

exports[`Tick Dynamic Difficulty Last Drop expect eliminate 3 candy then combine missile then drop rainbow 1`] = `
[
  "N5",
  "S1",
  "S4",
  "N3",
  "N1",
  "MBL1",
  "MBL1",
  "N1",
  "MBL1",
  "MBL1",
]
`;

exports[`Tick Dynamic Difficulty Last Drop expect generate rainbow 1`] = `
[
  "N1",
  "S1",
  "S4",
  "N4",
  "N5",
  "CI3_N1",
  "CI3_N1",
  "N3",
  "CI3_N1",
  "CI3_N1",
]
`;

exports[`Tick Dynamic Difficulty Last Drop expect generate rainbow after crush and has 4 blanks 1`] = `
[
  "S4",
  "S1",
  "N3",
  "N1",
  "N4",
  "N5",
  "N5",
  "N4",
  "N1",
  "N2",
]
`;

exports[`Tick Dynamic Difficulty Last Drop expect no props in 3 blank but cannot combine props 1`] = `
[
  "MBL1",
  "MCK1",
  "N4",
  "N4",
  "N3",
  "N4",
  "N5",
  "N3",
  "N3",
  "N5",
]
`;

exports[`Tick Dynamic Difficulty Last Drop expect try combo to generate missile 1`] = `
[
  "S4",
  "S1",
  "N1",
  "N4",
  "N4",
  "N5",
  "N1",
  "N3",
  "N4",
  "N1",
]
`;
