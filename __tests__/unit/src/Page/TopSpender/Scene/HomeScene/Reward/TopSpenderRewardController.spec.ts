import SceneController from '@/Page/Common/Scene/SceneController';
import { SCENE_TYPE } from '@/Page/Common/Scene/SceneTypes';
import TopSpenderRewardController from '@/Page/TopSpender/Scene/HomeScene/Reward/TopSpenderRewardController';
import logger from '@/Utils/logger';

jest.mock('@/Page/Common/Activity/Service', () => {
    return {
        __esModule: true,
        default: {},
        getActivityRankRewards: jest.fn().mockImplementation(() => { return {}; })
    };
});

describe('TopSpenderRewardController', () => {

    const originXHR = global.XMLHttpRequest;
    const mockXhrResponse = responseJSON => {
        class mockXMLHttpRequest extends XMLHttpRequest {
            open = jest.fn().mockImplementation(() => { });
            send = jest.fn().mockImplementation(() => {
                this.dispatchEvent(new Event('load'));
                this.dispatchEvent(new Event('readystatechange'));
            });
            setRequestHeader = jest.fn();
            get status() {
                return 200;
            }
            get readyState() {
                return 4;
            }
            get response() {
                return JSON.stringify(responseJSON);
            }
        }
        global.XMLHttpRequest = mockXMLHttpRequest;
    };

    async function getTopSpenderRewardControllerInstance() {
        const TopSpenderRewardController = (await import('@/Page/TopSpender/Scene/HomeScene/Reward/TopSpenderRewardController')).default;
        return TopSpenderRewardController.getIns();
    }

    afterEach(() => {
        jest.resetModules();
        global.XMLHttpRequest = originXHR;
    });

    it('instance should be an instanceof TopSpenderRewardController', async () => {
        const TopSpenderRewardController = (await import('@/Page/TopSpender/Scene/HomeScene/Reward/TopSpenderRewardController')).default;
        const instance = TopSpenderRewardController.getIns();
        expect(instance instanceof TopSpenderRewardController).toBeTruthy();
    });

    it('should have a method openRewardView()', async () => {
        const instance = await getTopSpenderRewardControllerInstance();
        const openRewardView = instance.openRewardView;
        // openRewardView(inTopSpender);
        expect(openRewardView).toBeTruthy();
    });

    it('should have a method destroy()', async () => {
        const instance = await getTopSpenderRewardControllerInstance();
        const destroy = instance.destroy;
        // destroy(needClearModel);
        expect(destroy).toBeTruthy();
    });


    // 600902:redis error
    it('should service method getTopSpenderGroupRank returns error code 600902', async () => {
        const mockData = (await import('./mockData/getTopSpenderGroupRank.mockData')).getTopSpenderGroupRankMockData;
        mockXhrResponse({ code: 600902, data: mockData, msg: '' });
        SceneController.model.curSceneKey = SCENE_TYPE.HOME;
        const loggerErr = jest.spyOn(logger, 'error');
        await TopSpenderRewardController.getIns().openRewardView();
        expect(loggerErr).toHaveBeenCalled();
    });

    // 6000614:activity closed
    it('should service method getTopSpenderGroupRank returns error code 6000614', async () => {
        const mockData = (await import('./mockData/getTopSpenderGroupRank.mockData')).getTopSpenderGroupRankMockData;
        mockXhrResponse({ code: 6000614, data: mockData, msg: '' });
        SceneController.model.curSceneKey = SCENE_TYPE.TOP_SPENDER_ENTRY;
        const loggerErr = jest.spyOn(logger, 'error');
        await TopSpenderRewardController.getIns().openRewardView();
        expect(loggerErr).toHaveBeenCalled();
    });

});
