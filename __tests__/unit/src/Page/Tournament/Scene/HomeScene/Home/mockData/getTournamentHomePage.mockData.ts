export const getTournamentHomePageMockData = {
    'startTime': 1,
    'endTime': 1,
    'playEndTime': 1,
    'battlePassScore': 1,
    'battlePassStageList': [
        {
            'score': 1,
            'stageRewards': [
                {
                    'type': 1,
                    'receiveStatus': 1,
                    'rewardImage': 'string',
                    'rewards': [
                        {
                            'rewardType': 1,
                            'displayNum': 'string',
                            'prizeIcon': 'string',
                            'ldcID': 1,
                            'ldcDrawResult': {
                                'drewRewardType': 1,
                                'drewDisplayNum': 'string',
                                'drewPrizeIcon': 'string',
                                'drewVoucherID': 1,
                                'defaultRewards': [
                                    {
                                        'rewardType': 1,
                                        'displayNum': 'string'
                                    }
                                ]
                            }
                        }
                    ]
                }
            ]
        }
    ],
    'rankConfigList': [
        {
            'rankIndex': 1,
            'rankIcon': 'string',
            'smallIcon': 'string',
            'stages': [
                1
            ]
        }
    ],
    'offlineSubmitRewards': [
        {
            'rewardType': 1,
            'displayNum': 'string',
            'prizeIcon': 'string',
            'ldcID': 1,
            'ldcDrawResult': {
                'drewRewardType': 1,
                'drewDisplayNum': 'string',
                'drewPrizeIcon': 'string',
                'drewVoucherID': 1,
                'defaultRewards': [
                    {
                        'rewardType': 1,
                        'displayNum': 'string'
                    }
                ]
            }
        }
    ]
};
