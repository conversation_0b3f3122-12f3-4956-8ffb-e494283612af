import { transUserAvatar } from '@/Components/SelfComponent/BaseAvatarImage';
import { getBits, setBits } from '@/Utils/Other/TickCore/MathUtils';
import { isStandardCdnPath } from '@/Utils/Shopee/CDNUtils';
import { dateFormatByRegion } from '@/Utils/Time/DateUtils';
import { addMaskToText } from '@/Utils/UI/TextUtils';
import sinon from 'sinon';

describe('Utils', () => {
    test('isStandardCdnPath', () => {
        const paths = [
            // cf域名资源 无资源后缀
            { path: 'https://cf.shopee.co.id/file/ff30fbcf88ee265832e5c743b7e849ef', result: true },
            // cf域名资源 无资源后缀 重试query
            { path: 'https://cf.shopee.co.id/file/ff30fbcf88ee265832e5c743b7e849ef?timestamp=1654134702860&retry=1', result: true },
            // deo webp域名资源
            { path: 'https://deo.shopeemobile.com/shopee/mkt/games/file/tp-4a3fa0c0-a3cf-45c6-9d32-26a49477690b-1653910696661/home-webp.webp', result: true },
            // deo webp域名资源 重试query
            { path: 'https://deo.shopeemobile.com/shopee/mkt/games/file/tp-4a3fa0c0-a3cf-45c6-9d32-26a49477690b-1653910696661/home-webp.webp?timestamp=1654134702860', result: true },
            // deo json域名资源
            { path: 'https://deo.shopeemobile.com/shopee/mkt/games/file/tp-4a3fa0c0-a3cf-45c6-9d32-26a49477690b-1653910696661/home-webp.json', result: true },
            // deo json域名资源 重试query
            { path: 'https://deo.shopeemobile.com/shopee/mkt/games/file/tp-4a3fa0c0-a3cf-45c6-9d32-26a49477690b-1653910696661/home-webp.json?timestamp=1654134702860', result: true },
            // deo域名资源 龙骨资源
            { path: 'https://deo.shopeemobile.com/shopee/shopee-candycrush-live-id/resource/main/assets/armature/prop/swap_ske_7c3f4df0c88029f505095f8ba1e7f6b6.json', result: true },
            // deo域名资源 龙骨资源 重试query
            { path: 'https://deo.shopeemobile.com/shopee/shopee-candycrush-live-id/resource/main/assets/armature/prop/swap_ske_7c3f4df0c88029f505095f8ba1e7f6b6.json?timestamp=1654134702860', result: true },
            // mp3资源
            { path: 'https://deo.shopeemobile.com/shopee/mkt/games/audio/d40b0a8fff7c544dc742bbb98a786d00/map_bg_new.mp3', result: true },
            // mp3资源 重试query
            { path: 'https://deo.shopeemobile.com/shopee/mkt/games/audio/d40b0a8fff7c544dc742bbb98a786d00/map_bg_new.mp3?timestamp=1654134702860', result: true },
            // fnt资源
            { path: 'https://deo.shopeemobile.com/shopee/shopee-candycrush-live-sg/resource/main/assets/fonts/32_0c24dfd89c9e63cb550d018d220275aa.fnt', result: true },
            // fnt资源 重试query
            { path: 'https://deo.shopeemobile.com/shopee/shopee-candycrush-live-sg/resource/main/assets/fonts/32_0c24dfd89c9e63cb550d018d220275aa.fnt?timestamp=1654134702860', result: true },
            // 二进制资源
            { path: 'https://deo.shopeemobile.com/shopee/shopee-candycrush-live-sg/resource/main/assets/armature/multi-mode/bin/baffle_bin_ske.dbbin', result: false },
            // 二进制资源
            { path: 'https://deo.shopeemobile.com/shopee/shopee-candycrush-live-sg/resource/main/assets/armature/multi-mode/bin/baffle_bin_ske.dbbin?timestamp=1654134702860', result: false },
            // 错误资源
            { path: 'https://deo.shopeemobile.com/shopee/shopee-candycrush-live-sg/props_new_json.green_panel', result: false },
            // 错误资源 重试
            { path: 'https://deo.shopeemobile.com/shopee/shopee-candycrush-live-sg/props_new_json.green_panel?timestamp=1654134702860', result: false },
            { path: 'default_avatar', result: false },
            { path: 'https://games.deo.shopeemobile.com/shopee/mkt/games/file/tp-6fe2de68-5ca7-478a-a2a8-ecc756f0de56-1697523616045/GroupRaceForGame_json-webp.webp', result: true },
        ];
        paths.forEach((f) => {
            expect(isStandardCdnPath(f.path)).toBe(f.result);
        });
    });

    test('addMaskToText', () => {
        const texts = [
            { text: 'Hello World', result: 'H***d' },
            { text: ' Hello World', result: 'H***d' },
            { text: 'H', result: 'H***' },
            { text: '', result: '' },
            { text: ' ', result: '' },
            { text: ' H', result: 'H***' },
            { text: 'HD', result: 'H***D' },
            { text: '中文', result: '中***文' },
        ];

        texts.forEach((f) => {
            expect(addMaskToText(f.text)).toBe(f.result);
        });
    });

    describe('transUserAvatar', () => {
        let hasResStub: sinon.SinonStub<[key: string], boolean>;

        beforeAll(() => {
            hasResStub = sinon.stub(RES, 'hasRes').callsFake((key) => {
                return ['default_avatar', 'N1', 'middleNormal_json.N1'].includes(key);
            });
        });

        afterAll(() => {
            hasResStub.restore();
        });

        const paths = [
            // URL 资源
            { path: 'https://cf.shopee.sg/file/43b64e287fbb756a971c2abf6e36e8bd', result: 'https://cf.shopee.sg/file/43b64e287fbb756a971c2abf6e36e8bd_tn' },
            { path: 'https://cf.shopee.sg/file/tw-11134004-7r98o-loxnwxm0ps007d', result: 'https://cf.shopee.sg/file/tw-11134004-7r98o-loxnwxm0ps007d_tn' },
            // hash 值
            { path: '43b64e287fbb756a971c2abf6e36e8bd', result: 'https://cf.shopee.sg/file/43b64e287fbb756a971c2abf6e36e8bd_tn' },
            { path: 'tw-11134004-7r98o-loxnwxm0ps007d', result: 'https://cf.shopee.sg/file/tw-11134004-7r98o-loxnwxm0ps007d_tn' },
            { path: 'id-11134233-7r98q-ll875c5m8dksb9', result: 'https://cf.shopee.sg/file/id-11134233-7r98q-ll875c5m8dksb9_tn' },
            // 缩略图资源
            { path: 'https://cf.shopee.sg/file/43b64e287fbb756a971c2abf6e36e8bd_tn', result: 'https://cf.shopee.sg/file/43b64e287fbb756a971c2abf6e36e8bd_tn' },
            // 无 hash 值的 URL
            { path: 'https://cf.shopee.sg/', result: 'default_avatar' },
            { path: 'https://cf.shopee.sg/file/', result: 'default_avatar' },
            // 本地资源
            { path: 'default_avatar', result: 'default_avatar' },
            { path: 'N1', result: 'N1' },
            { path: 'middleNormal_json.N1', result: 'middleNormal_json.N1' },
            // 错误资源
            { path: 'random string', result: 'default_avatar' },
            { path: '', result: 'default_avatar' },
        ];

        paths.forEach(p => {
            it(`transUserAvatar ${p.path}`, () => {
                expect(transUserAvatar(p.path)).toBe(p.result);
            });
        });
    });

    describe('getBits & setBits', () => {
        test('设置从第 0 位开始的 4 位二进制值', () => {
            const num = 0b10101010;
            const result = setBits(num, 0b1001, 0, 4);
            expect(result).toBe(0b10101001);
        });

        test('设置从第 3 位开始的 4 位二进制值', () => {
            const num = 0b10101010;
            const result = setBits(num, 0b0110, 3, 4);
            expect(result).toBe(0b10110010);
        });

        test('设置从第 5 位开始的 5 位二进制值', () => {
            const num = 0b10101010;
            const result = setBits(num, 0b11010, 5, 5);
            expect(result).toBe(0b1101001010);
        });

        test('设置超出范围的 value 应抛出错误', () => {
            const num = 0b10101010;
            expect(() => setBits(num, 0b100000, 3, 5)).toThrow('Value must be between 0 and 31');
        });

        test('设置超出范围的 offset 或 bitLength 应抛出错误', () => {
            const num = 0b10101010;
            expect(() => setBits(num, 0b1010, 30, 5)).toThrow('Invalid offset or bitLength');
        });

        test('提取从第 0 位开始的 4 位二进制值', () => {
            const num = 0b10101001;
            const result = getBits(num, 0, 4);
            expect(result).toBe(0b1001);
        });

        test('提取从第 3 位开始的 4 位二进制值', () => {
            const num = 0b10110110;
            const result = getBits(num, 3, 4);
            expect(result).toBe(0b0110);
        });

        test('提取从第 5 位开始的 5 位二进制值', () => {
            const num = 0b11101010;
            const result = getBits(num, 5, 5);
            expect(result).toBe(0b00111);
        });

        test('提取超出范围的 offset 或 bitLength 应抛出错误', () => {
            const num = 0b10101010;
            expect(() => getBits(num, 30, 5)).toThrow('Invalid offset or bitLength');
        });
    });

    describe('dateFormatByRegion', () => {
        const fixedTimestamp = Date.UTC(2024, 5, 1, 12, 34, 56); // 2024-06-01 12:34:56 UTC

        it('formats date for Asia/Singapore (sg)', () => { // UTC+8
            expect(dateFormatByRegion(fixedTimestamp, 'yyyy-MM-dd hh:mm:ss', 'sg')).toBe('2024-06-01 20:34:56');
        });
        it('formats date for Asia/Manila (ph)', () => { // UTC+8
            expect(dateFormatByRegion(fixedTimestamp, 'yyyy-MM-dd hh:mm:ss', 'ph')).toBe('2024-06-01 20:34:56');
        });
        it('formats date for Asia/Kuala_Lumpur (my)', () => { // UTC+8
            expect(dateFormatByRegion(fixedTimestamp, 'yyyy-MM-dd hh:mm:ss', 'my')).toBe('2024-06-01 20:34:56');
        });
        it('formats date for Asia/Taipei (tw)', () => { // UTC+8
            expect(dateFormatByRegion(fixedTimestamp, 'yyyy-MM-dd hh:mm:ss', 'tw')).toBe('2024-06-01 20:34:56');
        });
        it('formats date for Asia/Bangkok (th)', () => { // UTC+7
            expect(dateFormatByRegion(fixedTimestamp, 'yyyy-MM-dd hh:mm:ss', 'th')).toBe('2024-06-01 19:34:56');
        });
        it('formats date for Asia/Ho_Chi_Minh (vn)', () => { // UTC+7
            expect(dateFormatByRegion(fixedTimestamp, 'yyyy-MM-dd hh:mm:ss', 'vn')).toBe('2024-06-01 19:34:56');
        });
        it('formats date for Asia/Jakarta (id)', () => { // UTC+7
            expect(dateFormatByRegion(fixedTimestamp, 'yyyy-MM-dd hh:mm:ss', 'id')).toBe('2024-06-01 19:34:56');
        });
        it('formats date for America/Mexico_City (mx)', () => { // UTC-6
            expect(dateFormatByRegion(fixedTimestamp, 'yyyy-MM-dd hh:mm:ss', 'mx')).toBe('2024-06-01 06:34:56');
        });
        it('formats date for America/Sao_Paulo (br)', () => { // UTC-3
            expect(dateFormatByRegion(fixedTimestamp, 'yyyy-MM-dd hh:mm:ss', 'br')).toBe('2024-06-01 09:34:56');
        });
        it('formats date for Europe/Warsaw (pl)', () => { // UTC+1
            expect(dateFormatByRegion(fixedTimestamp, 'yyyy-MM-dd hh:mm:ss', 'pl')).toBe('2024-06-01 13:34:56');
        });
        it('formats with different format string', () => {
            expect(dateFormatByRegion(fixedTimestamp, 'dd/MM/yyyy hh:mm', 'sg')).toBe('01/06/2024 20:34');
        });
        it('uses window.REGION if region not specified', () => {
            const originalRegion = window.COUNTRY;
            window.COUNTRY = 'ph';
            expect(dateFormatByRegion(fixedTimestamp, 'yyyy-MM-dd hh:mm:ss')).toBe('2024-06-01 20:34:56');
            window.COUNTRY = originalRegion;
        });
        it('formats with seconds omitted', () => {
            expect(dateFormatByRegion(fixedTimestamp, 'yyyy-MM-dd hh:mm', 'sg')).toBe('2024-06-01 20:34');
        });
        it('formats with only date', () => {
            expect(dateFormatByRegion(fixedTimestamp, 'yyyy.MM.dd', 'sg')).toBe('2024.06.01');
        });
    });

});
