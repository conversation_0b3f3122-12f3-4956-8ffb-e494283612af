import { STORE_KEY, STORE_KEY_NOUID } from '@/Config/StorageConfig';
import State from '@/State';
import Store, { formatKey } from '@/Utils/Storage/Store';

jest.mock('@/Page/Main/HomeScene/Gift/GiftModalType');
jest.mock('@/Page/Main/HomeScene/Event/TaskModal/StageRush/TaskStageRushController');

describe('Store Test', () => {
    const mockUser = { userid: 'testUser' } as any;
    beforeAll(() => {
        State.user = mockUser;
    });

    beforeEach(() => {
        localStorage.clear();
        Store.storeDic = {};
    });

    it('should store and retrieve a value', () => {
        const key = STORE_KEY_NOUID.USER_BASE_INFO;
        const value = { name: 'testName', age: 30 };

        Store.set(key, value, false);
        const retrievedValue = Store.get(key, false);

        expect(retrievedValue).toEqual(value);
    });

    it('should remove a stored value', () => {
        const key = STORE_KEY_NOUID.USER_BASE_INFO;
        const value = { name: 'testName', age: 30 };

        Store.set(key, value, false);
        Store.remove(key, false);

        const retrievedValue = Store.get(key, false);
        expect(retrievedValue).toBeNull();
    });

    it('should get and set user-specific keys', () => {
        const key = 'TEST_KEY';
        const value = { test: 'value' };
        Store.set(key, value, true);
        const formattedKey = `CANDY_CRUSH_TEST_KEY_${mockUser.userid}`;

        expect(localStorage.getItem(formattedKey)).toEqual(JSON.stringify(value));

        const retrievedValue = Store.get(key, true);
        expect(retrievedValue).toEqual(value);
    });

    it('should handle module and key correctly', () => {
        const moduleKey = STORE_KEY.STORAGE_GUIDE;
        const value = { guide: 'step1' };

        Store.set(moduleKey, value, true);

        const storedModuleData = localStorage.getItem('CANDY_CRUSH_HOME');
        expect(storedModuleData).not.toBeNull();

        if (storedModuleData) {
            const parsedModuleData = JSON.parse(storedModuleData);
            const storedValue = parsedModuleData[`STORAGE_GUIDE_${mockUser.userid}`];
            expect(storedValue).toEqual(value);
        }

        const retrievedValue = Store.get(moduleKey, true);
        expect(retrievedValue).toEqual(value);
    });

    it('should format key correctly', () => {
        const key = 'TEST_KEY';
        const formattedKey = formatKey(key, true);
        expect(formattedKey).toBe(`TEST_KEY_${mockUser.userid}`);
    });

    it('should handle cache correctly', () => {
        for (let i = 0; i < 210; i++) {
            Store.set(`TEST_KEY_${i}`, { test: `value${i}` }, false);
        }

        expect(Object.keys(localStorage).length).toBe(10);
    });

    it('should delete specified items', () => {
        const keyInfoObjs = [
            { partKey: 'TEST_KEY', excludePartKeys: [] },
        ];

        Store.set('TEST_KEY_1', { test: 'value1' }, false);
        Store.set('TEST_KEY_2', { test: 'value2' }, false);

        Store.delete(keyInfoObjs, false);
        expect(localStorage.getItem('TEST_KEY_1')).toBeNull();
        expect(localStorage.getItem('TEST_KEY_2')).toBeNull();
    });

    it('should get and set items with expiration', () => {
        const key = STORE_KEY_NOUID.STORAGE_LOGIN_EXPIRED_TIME;
        const value = 'testValue';
        const expiredTime = 1; // 1 second
        // 使用 Jest 的 fake timers
        jest.useFakeTimers();

        Store.setItemWithExpired(key, value, expiredTime);
        let retrievedValue = Store.getItemWithExpired(key);

        expect(retrievedValue).toBe(value);

        // 快速推进时间
        jest.advanceTimersByTime((expiredTime + 1) * 1000);

        retrievedValue = Store.getItemWithExpired(key);
        expect(retrievedValue).toBeNull();
        // 恢复真实的时间
        jest.useRealTimers();
    });
});
