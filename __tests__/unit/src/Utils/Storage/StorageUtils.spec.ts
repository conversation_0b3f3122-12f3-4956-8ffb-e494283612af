import { PAGE_TYPE } from '@/Config/PageConfig';
import { STORE_KEY } from '@/Config/StorageConfig';
import ApolloController from '@/Controller/ApolloController';
import PlatformModel from '@/Controller/Platform/PlatformModel';
import { initMiniGameStoreByPage } from '@/Utils/Storage/StorageUtils';
import Store from '@/Utils/Storage/Store';


describe('initMiniGameStoreByPage test', () => {
    beforeEach(() => {
        ApolloController['_useActivityConfig'] = [];
    });
    PlatformModel.otherActivityCode[PAGE_TYPE.TOURNAMENT] = 'activityCode';
    PlatformModel.otherSlotCode[PAGE_TYPE.TOURNAMENT] = 'slotCode';
    const combineASID = `${PlatformModel.otherActivityCode[PAGE_TYPE.TOURNAMENT]}_${PlatformModel.otherSlotCode[PAGE_TYPE.TOURNAMENT]}`;
    it('use slot code', () => {
        initMiniGameStoreByPage(PAGE_TYPE.TOURNAMENT, PAGE_TYPE.TOURNAMENT);
        expect(Store.get(PAGE_TYPE.TOURNAMENT).sid).toBe(combineASID);
    });

    it('use activity code', () => {
        ApolloController['_useActivityConfig'] = [PAGE_TYPE.TOURNAMENT];
        initMiniGameStoreByPage(PAGE_TYPE.TOURNAMENT, PAGE_TYPE.TOURNAMENT);
        expect(Store.get(PAGE_TYPE.TOURNAMENT).sid).toBe(PlatformModel.otherActivityCode[PAGE_TYPE.TOURNAMENT]);
    });

    it('use activity first, then use slot', () => {
        ApolloController['_useActivityConfig'] = [PAGE_TYPE.TOURNAMENT];
        initMiniGameStoreByPage(PAGE_TYPE.TOURNAMENT, PAGE_TYPE.TOURNAMENT);
        expect(Store.get(PAGE_TYPE.TOURNAMENT).sid).toBe(PlatformModel.otherActivityCode[PAGE_TYPE.TOURNAMENT]);
        ApolloController['_useActivityConfig'] = [];
        initMiniGameStoreByPage(PAGE_TYPE.TOURNAMENT, PAGE_TYPE.TOURNAMENT);
        expect(Store.get(PAGE_TYPE.TOURNAMENT).sid).toBe(combineASID);
    });

    it('old client version have entry new slot mini-game, new client entry solve', () => {
        Store.set(STORE_KEY.TOURNAMENT, { aid: PlatformModel.otherActivityCode[PAGE_TYPE.TOURNAMENT], guide: 1, rank: 100 });
        initMiniGameStoreByPage(PAGE_TYPE.TOURNAMENT, PAGE_TYPE.TOURNAMENT);
        expect(Store.get(PAGE_TYPE.TOURNAMENT).sid).toBe(combineASID);
        expect(Store.get(PAGE_TYPE.TOURNAMENT).guide).toBe(1);
        expect(Store.get(PAGE_TYPE.TOURNAMENT).rank).toBe(100);
    });

});